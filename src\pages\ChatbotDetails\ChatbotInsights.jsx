import { useCallback, useEffect, useRef, useState } from 'react';
import { DateTime } from 'luxon';
import { useNavigate, useParams } from 'react-router-dom';
import DButton from '@/components/Global/DButton';
import DCheckbox from '@/components/Global/DCheckbox';
import DSelect from '@/components/Global/DSelect';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import ExportIcon from '@/components/Global/Icons/ExportIcon';
import FlagIcon from '@/components/Global/Icons/FlagIcon';
import UpRightIcon from '@/components/Global/Icons/UpRightIcon';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as conversationService from '@/services/conversations.service';
import * as statiscticsService from '@/services/statisctics.service';
import * as chatbotService from '@/services/chatbot.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import { ListboxOption } from '@headlessui/react';
import InsightsChart from './InsightsChart';
import DTable from '@/components/Global/DTable';
import DButtonIcon from '@/components/Global/DButtonIcon';
import ChevronLeftIcon from '@/components/Global/Icons/ChevronLeftIcon';
import DModal from '@/components/Global/DModal';
import generateFilename from '@/helpers/generateFileName';
import DCircularChart from '@/components/Global/DCircularChart';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useToast from '@/hooks/useToast';
import DModalExportInsights from '@/components/DModalExportInsights';
import DLoading from '@/components/DLoading';
import DDateRangePicker from '@/components/DDateRangePicker';
import DModalExportFormat from '@/components/DModalExportFormat';
import featureCheck, { checkFeatureAvailability } from '@/helpers/tier/featureCheck';
import GlobalModals from '@/components/GlobalModals';
import InlineGlobalModals from '@/components/InlineGlobalModals';
import useModalStore from '@/stores/modal/modalStore';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useTimezone } from '@/hooks/useTimezone';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

const ChatbotInsights = () => {
  //store
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const openPlansModal = useModalStore((state) => state.openPlansModal);
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);
  //hooks
  const params = useParams();
  const navigate = useNavigate();

  //states
  const [seeAllRecords, setSeeAllRecords] = useState(false);
  const [chatRecord, setChatRecord] = useState('');
  const [seeAllLeadGen, setSeeAllLeadGen] = useState(false);

  //filters
  const [showMessages, setShowMessages] = useState(true);
  const [showCredits, setShowCredits] = useState(true);
  const [showUniqueUsers, setShowUniqueUsers] = useState(true);
  const [showConversations, setShowConversations] = useState(true);
  const [timeRange, setTimeRange] = useState({
    date_from: DateTime.now()
      .minus({ days: 30 })
      .startOf('day')
      .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    date_to: DateTime.now().endOf('day').toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
  });

  const [isFirstTimeStatistics, setIsFirstTimeStatistics] = useState(true);
  const [openLeadModal, setOpenLeadModal] = useState('');
  const [exportInsights, setExportInsights] = useState(false);
  const [isLoadingRecords, setIsLoadingRecords] = useState(false);
  const [isLoadingLeads, setIsLoadingLeads] = useState(false);
  const [dateSelectedRecords, setDateSelectedRecords] = useState('Last 30 days');
  const [dateSelectedLeads, setDateSelectedLeads] = useState('Last 30 days');
  const [dateSelectedGeneral, setDateSelectedGeneral] = useState('Last 30 days');
  const [localSelectedRows, setLocalSelectedRows] = useState(new Set());
  const [exportFormatModal, setExportFormatModal] = useState({
    isOpen: false,
    type: '' // 'records', 'leads', or 'all'
  });

  const timezone = useTimezoneStore((state) => state.timezone);


  const filterOptions = [
    {
      label: 'Week to date',
      value: DateTime.now()
        .startOf('week')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Month to date',
      value: DateTime.now()
        .startOf('month')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Last 7 days',
      value: DateTime.now()
        .minus({ days: 7 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Last 30 days',
      value: DateTime.now()
        .minus({ days: 30 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Last 90 days',
      value: DateTime.now()
        .minus({ days: 90 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Custom',
      value: 'custom',
    },
  ];

  //api
  const { data: statistics, refetch, isLoading } = useDanteApi(
    statiscticsService.getStatistics,
    [dateSelectedGeneral, timeRange.date_from, timeRange.date_to],
    {},
    params.id,
    timeRange.date_from,
    timeRange.date_to,
    timezone
  );

  const { data } = useDanteApi(
    conversationService.getSharedConversations,
    [dateSelectedRecords],
    {},
    params.id,
    timeRange.date_from,
    timeRange.date_to,
    timezone
  );

  const { data: generalData } = useDanteApi(
    statiscticsService.getGeneralData,
    [], // No time range dependency here
    {},
    params.id,
    timezone
  );

  const { data: leadGenData } = useDanteApi(
    chatbotService.getChatbotLeadGen,
    [dateSelectedLeads],
    {},
    params.id,
    timeRange.date_from,
    timeRange.date_to,
    timezone
  );

  //toast
  const { addErrorToast } = useToast();

  const handleExportLeads = async (format) => {
    setIsLoadingLeads(true);
    try {
      if (leadGenData.results.length === 0) {
        addErrorToast({ message: 'No records found' });
        return;
      }
      const response = await conversationService.downloadLeads(
        params.id,
        timeRange.date_from,
        timeRange.date_to,
        format,
        timezone
      );
      if (response.status === 200) {
        const download_url = response.data.download_url;
        window.open(download_url, '_blank');
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingLeads(false);
      setExportFormatModal({ isOpen: false, type: '' });
    }
  };

  const handleExportRecords = async (format, type = 'by_date') => {
    setIsLoadingRecords(true);
    try {
      if (data.results.length === 0) {
        addErrorToast({ message: 'No records found' });
        return;
      }
      const dateFrom = type === 'all'
        ? DateTime.now().startOf('year').toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
        : timeRange.date_from;
      const dateTo = type === 'all'
        ? DateTime.now().endOf('day').toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
        : timeRange.date_to;

      const response = await conversationService.downloadChatRecordLog(
        params.id,
        dateFrom,
        dateTo,
        format,
        timezone
      );
      if (response.status === 200) {
        const download_url = response.data.download_url;
        window.open(download_url, '_blank');
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingRecords(false);
      setExportFormatModal({ isOpen: false, type: '' });
    }
  };

  const handleExportFormat = (format) => {
    switch (exportFormatModal.type) {
      case 'leads':
        handleExportLeads(format);
        break;
      case 'records':
        handleExportRecords(format, 'by_date');
        break;
      case 'all':
        handleExportRecords(format, 'all');
        break;
    }
  };

  const handleDateRangeChange = (fromDate, toDate) => {
    setTimeRange({
      date_from: DateTime.fromISO(fromDate).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
      date_to: DateTime.fromISO(toDate).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
      label: 'custom'
    });
  };

  // useEffect(() => {
  //   if (!isFirstTimeStatistics) {
  //     refetch();
  //   }
  // }, [timeRange]);

  useEffect(() => {
    setSidebarOpen(false);
    if (window.innerWidth < 768) {
      setLayoutTitle('Chatbot');
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth]);

  useEffect(() => {
    if (teamSelected && featureCheck('insights', true)) {
      return;
    }

    if (!teamSelected && featureCheck('analytics_dashboard', true)) {
      return;
    }
  }, []);

  if(!data || !leadGenData || !generalData || !statistics) {
    return <DLoading show={true} />
  }

  return (
    <div className="flex flex-col md:flex-row gap-size3 h-full w-full overflow-hidden">
      {!seeAllRecords && !chatRecord && !seeAllLeadGen && (
        <div className="h-full overflow-y-auto bg-white rounded-size1 p-size5 flex flex-col gap-size2 w-full relative">
          <InlineGlobalModals />

          <div className="flex gap-size2 flex-col md:flex-row">
          <div className="border border-grey-5 rounded-size1 p-size5 flex flex-col gap-size3 w-full">
              <p className="text-xl font-medium tracking-tight">All time</p>
              <div className="flex gap-size1 py-size0">
                <div className="w-px bg-grey-300 h-5"></div>
                <p className="text-base tracking-tight">
                  Online since{' '}
                  {DateTime.fromISO(generalData?.online_since).toFormat(
                    'd MMM yyyy'
                  )}
                </p>
              </div>
              <div className="w-full h-px border border-grey-5"></div>
              <div className="flex items-center justify-between flex-wrap">
                <div className="flex flex-col gap-size0 w-1/2 md:w-auto">
                  <p className="text-lg font-medium tracking-tight">
                    {generalData?.credits_used}
                  </p>
                  <p className="text-xs font-regular tracking-tight text-grey-50">
                    Credits used
                  </p>
                </div>
                <div className="flex flex-col gap-size0 w-1/2 md:w-auto">
                  <p className="text-lg font-medium tracking-tight">
                    {generalData?.unique_users}
                  </p>
                  <p className="text-xs font-regular tracking-tight text-grey-50">
                    Unique users
                  </p>
                </div>
                <div className="flex flex-col gap-size0 w-1/2 md:w-auto">
                  <p className="text-lg font-medium tracking-tight">
                    {generalData?.conversations}
                  </p>
                  <p className="text-xs font-regular tracking-tight text-grey-50">
                    Conversations
                  </p>
                </div>
                <div className="flex flex-col gap-size0 w-1/2 md:w-auto">
                  <p className="text-lg font-medium tracking-tight">
                    {generalData?.messages}
                  </p>
                  <p className="text-xs font-regular tracking-tight text-grey-50">
                    Messages
                  </p>
                </div>
              </div>
              <div className="w-full h-px border border-grey-5"></div>
              <div className="flex flex-col gap-size2">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-3xl tracking-tighter">
                      {generalData?.satisfaction_rate}%
                    </p>
                    <p className="text-base tracking-tight">
                      Satisfaction Rate
                    </p>
                  </div>
                  <DCircularChart
                    value={generalData?.satisfaction_rate}
                    variant="positive"
                  />
                </div>
                <div className="w-full h-px border border-grey-5"></div>
                <p className="text-base tracking-tight">
                  <span className="font-medium">
                    {generalData?.flagged_messages}
                  </span>{' '}
                  messages out of <span className="font-medium">
                    {generalData?.messages}
                  </span> were flagged
                </p>
                {/* <DAlert
                  state="negative"
                  className="items-center h-6 bg-negative-2"
                >
                  <p className="text-xs text-negative-100">
                    Review flagged records{' '}
                  </p>
                </DAlert> */}
              </div>
            </div>
              <div className="flex flex-col gap-size2 w-full">
                  <div className="border border-grey-5 rounded-size1 p-size5 flex flex-col gap-size3 w-full">
                    <div className="flex items-center justify-between">
                      <p className="text-xl font-medium">Chat records</p>
                      <DButton
                        variant="grey"
                        className="!h-[40px] !w-32"
                        onClick={() => setExportFormatModal({ isOpen: true, type: 'all' })}
                        loading={isLoadingRecords}
                      >
                        <ExportIcon />
                        <p className="text-base tracking-tight">Export All</p>
                      </DButton>
                    </div>
                    {data && data.results.length > 0 ? (
                      <div className="flex flex-col gap-size1 w-full">
                        {data.results.slice(0, 3).map((item, index) => (
                          <div
                            className={`flex justify-between items-center p-size1 ${
                              item.has_not_answered_questions
                                ? 'bg-negative-2 text-negative-100'
                                : 'bg-transparent text-black'
                            } border-b border-b-grey-5 `}
                            key={index}
                          >
                            <p className="text-base tracking-tight">
                              {item?.medium === 'whatsapp' ? item?.name : DateTime.fromISO(item.date_created).toFormat(
                                'd MMM yyyy - HH:mm:ss'
                              )}
                            </p>
                            <div className="flex items-center gap-size1">
                              {item.has_not_answered_questions && checkFeatureAvailability('unanswered_question_recognition') && (
                                <FlagIcon className="text-negative-100" />
                              )}
                              <button onClick={() => navigate(`records/${item.id}`)}>
                                <UpRightIcon />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-base tracking-tight">No records found</p>
                    )}
                    {data && data.results.length > 3 && (
                      <DButton
                        variant="grey"
                        className="!h-[40px]"
                        onClick={() => navigate('records')}
                      >
                        See all records
                      </DButton>
                    )}
                  </div>

                  <div className="border border-grey-5 rounded-size1 p-size5 flex flex-col gap-size3 w-full">
                    <div className="flex items-center justify-between">
                      <p className="text-xl font-medium">Lead list</p>
                      <DButton
                        variant="grey"
                        className="!h-[40px] !w-32"
                        onClick={() => handleExportLeads('csv')}
                        loading={isLoadingLeads}
                      >
                        <ExportIcon />
                        <p className="text-base tracking-tight">Export All</p>
                      </DButton>
                    </div>
                    {leadGenData && leadGenData.results.length > 0 ? (
                      <div className="flex flex-col gap-size1 w-full">
                      <table className="table-auto w-full border-collapse">
                        <tbody>
                          {leadGenData.results.slice(0, 3).map((item, index) => (
                            <tr key={index} className="border-b border-grey-5">
                              {Object.values(item).map((value, valueIndex) => (
                                <td
                                  key={valueIndex}
                                  className="p-2 text-left"
                                >
                                  {value}
                                </td>
                              ))}
                              <td>
                              <button
                                className="flex items-center justify-end  w-full"
                                onClick={() => setOpenLeadModal(item)}
                              >
                                <UpRightIcon />
                              </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    ) : (
                      <p className="text-base tracking-tight">No records found</p>
                    )}
                    {leadGenData && leadGenData.results.length > 3 && (
                      <DButton
                        variant="grey"
                        className="!h-[40px]"
                        onClick={() => setSeeAllLeadGen(true)}
                      >
                        See all records
                      </DButton>
                    )}
                  </div>
              </div>
          </div>
          <div className="border rounded-size1 border-grey-5 p-size4 flex flex-col gap-size3 overflow-y-auto min-h-[500px] md:min-h-auto">
            <div className="flex justify-between md:items-center flex-col gap-size2 lg:flex-row">
              <div className="flex gap-size4 w-full max-w-[100%] lg:max-w-[60%] xl:max-w-[70%] bg-red-500 flex-wrap lg:flex-nowrap lg:overflow-x-auto lg:pb-size0">
                <div className="flex items-start p-size1 rounded-size0 bg-orange-5 w-[47%] lg:w-full">
                  {' '}
                  {/* 50% width for mobile */}
                  <DCheckbox
                    checked={showCredits}
                    style={{ width: 'auto' }}
                    onChange={() => setShowCredits(!showCredits)} // Toggle visibility
                  />
                  <div className="flex flex-col w-full">
                    <p className="text-lg font-medium tracking-tight">
                      {statistics?.nr_credits}
                    </p>
                    <p className="text-xs text-grey-50 whitespace-nowrap">
                      Credits used
                    </p>
                  </div>
                </div>

                <div className="flex items-start p-size1 rounded-size0 bg-green-5 w-[47%] lg:w-full">
                  {' '}
                  {/* 50% width for mobile */}
                  <DCheckbox
                    checked={showUniqueUsers}
                    style={{ width: 'auto' }}
                    onChange={() => setShowUniqueUsers(!showUniqueUsers)} // Toggle visibility
                  />
                  <div className="flex flex-col w-full">
                    <p className="text-lg font-medium tracking-tight">
                      {statistics?.nr_users}
                    </p>
                    <p className="text-xs text-grey-50 whitespace-nowrap">
                      Unique users
                    </p>
                  </div>
                </div>

                <div className="flex items-start p-size1 rounded-size0 bg-purple-5 w-[47%] lg:w-full">
                  {' '}
                  {/* 50% width for mobile */}
                  <DCheckbox
                    checked={showConversations}
                    style={{ width: 'auto' }}
                    onChange={() => setShowConversations(!showConversations)} // Toggle visibility
                  />
                  <div className="flex flex-col w-full">
                    <p className="text-lg font-medium tracking-tight">
                      {statistics?.nr_conversations}
                    </p>
                    <p className="text-xs text-grey-50 whitespace-nowrap">
                      Conversations
                    </p>
                  </div>
                </div>

                <div className="flex items-start p-size1 rounded-size0 bg-negative-5 w-[47%] lg:w-full">
                  {' '}
                  {/* 50% width for mobile */}
                  <DCheckbox
                    checked={showMessages}
                    style={{ width: 'auto' }}
                    onChange={() => setShowMessages(!showMessages)} // Toggle visibility
                  />
                  <div className="flex flex-col w-full">
                    <p className="text-lg font-medium tracking-tight">
                      {statistics?.nr_messages}
                    </p>
                    <p className="text-xs text-grey-50 whitespace-nowrap">
                      Messages
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex  w-full xl-max:flex-col-reverse xl-max:items-end xl-max:w-auto justify-end gap-size1 bg-blue-500">
                {dateSelectedGeneral?.toLowerCase() === 'custom' && (
                  <DDateRangePicker
                    fromDate={timeRange.date_from}
                    toDate={timeRange.date_to}
                    onApply={(from, to) => {
                      setTimeRange({
                        date_from: DateTime.fromISO(from).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
                        date_to: DateTime.fromISO(to).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
                        label: 'custom'
                      });
                    }}
                  />
                )}
                <div className='flex gap-size1 w-full md:w-auto'>
                  <DSelect
                    listButtonClass="!h-[40px] !w-max !grow md:!grow-0"
                    options={filterOptions}
                    onChange={(value) => {
                      setIsFirstTimeStatistics(false);
                      setDateSelectedGeneral(filterOptions.find(option => option.value === value)?.label || 'custom');
                      value !== 'custom' && setTimeRange({ ...timeRange, date_from: value });
                    }}
                    selectedChild={dateSelectedGeneral}
                  />
                  <DButton variant="grey" className="!h-[40px] grow md:grow-0 !w-32" onClick={() => setExportInsights(true)}>
                    <ExportIcon />
                    <p className="text-base tracking-tight">Export</p>
                  </DButton>
                </div>
              </div>
            </div>
            {statistics && (
              <InsightsChart
                apiResponse={isLoading ? {} : statistics}
                showConversations={showConversations}
                showMessages={showMessages}
                showCredits={showCredits}
                showUniqueUsers={showUniqueUsers}
              />
            )}
          </div>
        </div>
      )}

      {seeAllLeadGen && (
        <div className="w-full h-full overflow-y-auto bg-white rounded-size5 p-size5 flex flex-col gap-size2">
          <DButtonIcon
            variant="outlined"
            onClick={() => setSeeAllLeadGen(false)}
            className="!py-size1"
          >
            <ChevronLeftIcon />
          </DButtonIcon>
          <div className="flex justify-between items-center gap-size1 w-full">
            <p className="text-xl font-medium tracking-tight">Lead list</p>
            <div className="flex gap-size1">
              <DButton
                variant="dark"
                className="!h-[40px] !w-32"
                onClick={() => setExportFormatModal({ isOpen: true, type: 'leads' })}
                loading={isLoadingLeads}
              >
                Export All
              </DButton>
              <DSelect
                listButtonClass="!h-[40px] !w-max !grow md:!grow-0"
                options={filterOptions}
                onChange={(value) => {
                  setDateSelectedLeads(filterOptions.find(option => option.value === value)?.label || 'custom');
                  value !== 'custom' && setTimeRange({ ...timeRange, date_from: value });
                }}
                selectedChild={dateSelectedLeads}
              />
              {dateSelectedLeads?.toLowerCase() === 'custom' && (
                <DDateRangePicker
                  fromDate={timeRange.date_from}
                  toDate={timeRange.date_to}
                  onApply={(from, to) => {
                    setTimeRange({
                      date_from: DateTime.fromISO(from).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
                      date_to: DateTime.fromISO(to).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
                      label: 'custom'
                    });
                  }}
                />
              )}
            </div>
          </div>
          <div className="flex flex-col gap-size1 w-full">
            <table className="table-fixed w-full border-collapse">
              <tbody>
                {leadGenData.results.map((item, index) => (
                  <tr key={index} className=' border-b border-grey-5 '>
                    <td className="p-2 text-center w-[60px]">
                      <DCheckbox
                        checked={localSelectedRows.has(index)}
                        onChange={(checked) => {
                          const allSelected = checked;
                          const newSelectedRows = allSelected
                            ? new Set(data.map((_, index) => index))
                            : new Set();
                          setLocalSelectedRows(newSelectedRows);
                        }}
                      />
                    </td>
                    {Object.values(item).map((value, valueIndex) => (
                      <td
                        key={valueIndex}
                        className="p-2 text-left"
                      >
                        {value !== null && value !== undefined ? value : ''}
                      </td>
                    ))}
                    {Array.from(
                      { length: Object.keys(leadGenData.results[0]).length - Object.keys(item).length }
                    ).map((_, emptyIndex) => (
                      <td
                        key={`empty-${emptyIndex}`}
                        className="p-2 text-left"
                      />
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      <DModal
        isOpen={openLeadModal !== ''}
        onClose={() => setOpenLeadModal('')}
        title="Lead list"
      >
        <div className="flex flex-col gap-size2">
          {Object.keys(openLeadModal).map((key) => (
            <div className="flex items-center gap-size1">
              <p className="text-lg font-medium tracking-tight capitalize">
                {key}
              </p>
              <p className="text-base tracking-tight">{openLeadModal[key]}</p>
            </div>
          ))}
        </div>
      </DModal>
      <DModalExportInsights
        open={exportInsights}
        onClose={() => setExportInsights(false)}
        date_created_from={timeRange.date_from}
        date_created_to={timeRange.date_to}
        selectedChatbot={selectedChatbot}
      />
      <DModalExportFormat
        open={exportFormatModal.isOpen}
        onClose={() => setExportFormatModal({ isOpen: false, type: '' })}
        onExport={handleExportFormat}
        loading={isLoadingLeads || isLoadingRecords}
      />
    </div>
  );
};

export default ChatbotInsights;
